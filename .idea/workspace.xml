<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="b0908632-2dc6-4c33-9086-451c8eaf1fba" name="Changes" comment="fix: 完成分块上传下载的测试">
      <change beforePath="$PROJECT_DIR$/src/infrastructure/providers/HuaweiObsProvider.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/infrastructure/providers/HuaweiObsProvider.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/infrastructure/providers/MinioProvider.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/infrastructure/providers/MinioProvider.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/test/infrastructure-test.html" beforeDir="false" afterPath="$PROJECT_DIR$/test/infrastructure-test.html" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2yadpGwaP5bi5JLDFC3D1rwGLXj" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="autoscrollFromSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.CodyAccountHistoryMigration": "true",
    "RunOnceActivity.CodyConvertUrlToCodebaseName": "true",
    "RunOnceActivity.CodyHistoryLlmMigration": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.ToggleCodyToolWindowAfterMigration": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "google",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "ts.external.directory.path": "/Users/<USER>/export/working/src/github.com/AIPlayZone/MemoryKeeper/node_modules/typescript/lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-WS-243.26053.12" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="b0908632-2dc6-4c33-9086-451c8eaf1fba" name="Changes" comment="" />
      <created>1750076798938</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750076798938</updated>
      <workItem from="1750076800392" duration="59000" />
      <workItem from="1750076864359" duration="4181000" />
      <workItem from="1750083152856" duration="128000" />
      <workItem from="1750083307794" duration="116000" />
      <workItem from="1750083663962" duration="114000" />
      <workItem from="1750083799818" duration="97000" />
      <workItem from="1750083905893" duration="1535000" />
      <workItem from="1750122793434" duration="3592000" />
    </task>
    <task id="LOCAL-00001" summary="fix: 完成分块上传下载的测试">
      <option name="closed" value="true" />
      <created>1750124283678</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1750124283678</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <MESSAGE value="fix: 完成分块上传下载的测试" />
    <option name="LAST_COMMIT_MESSAGE" value="fix: 完成分块上传下载的测试" />
  </component>
</project>