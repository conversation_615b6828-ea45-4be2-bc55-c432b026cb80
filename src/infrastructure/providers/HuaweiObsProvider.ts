// 华为云OBS SDK类型定义
interface ObsClient {
  headBucket(params: any, callback?: Function): any;
  getObject(params: any, callback?: Function): any;
  putObject(params: any, callback?: Function): any;
  deleteObject(params: any, callback?: Function): any;
  deleteObjects(params: any, callback?: Function): any;
  listObjects(params: any, callback?: Function): any;
  headObject(params: any, callback?: Function): any;
  initiateMultipartUpload(params: any, callback?: Function): any;
  uploadPart(params: any, callback?: Function): any;
  completeMultipartUpload(params: any, callback?: Function): any;
  abortMultipartUpload(params: any, callback?: Function): any;
  listMultipartUploads(params: any, callback?: Function): any;
}

// 声明全局的ObsClient构造函数（由华为云OBS SDK提供）
declare global {
  const ObsClient: {
    new (config: {
      access_key_id: string;
      secret_access_key: string;
      server: string;
      timeout?: number;
      max_retry_count?: number;
    }): ObsClient;
  };
}
import { BaseStorageProvider } from '../interfaces/IStorageProvider';
import { StorageType } from '../enums/StorageType';
import { IStorageConfig, ICloudStorageConfig } from '../types/StorageConfig';
import {
  StorageResult,
  StorageResultFactory,
  ObjectMetadata,
  GetOptions,
  PutOptions,
  ListOptions,
  SignedUrlOptions,
  BatchOptions,
  StreamOptions,
  StorageStats,
  MultipartUploadOptions,
  MultipartUploadInfo,
  UploadPartInfo,
  ProgressInfo
} from '../types/StorageResult';

/**
 * 华为云OBS存储提供者
 * 实现IStorageProvider接口，提供华为云对象存储服务
 */
export class HuaweiObsProvider extends BaseStorageProvider {
  private obsClient: ObsClient | null = null;
  private bucketName: string = '';
  
  constructor() {
    super('华为云OBS', StorageType.HUAWEI_OBS);
  }
  
  /**
   * 初始化华为云OBS客户端
   */
  async initialize(config: IStorageConfig): Promise<void> {
    try {
      const obsConfig = config as ICloudStorageConfig;

      // 验证配置参数
      this.validateConfig(obsConfig);

      // 检查华为云OBS SDK是否可用
      if (typeof window !== 'undefined' && typeof (window as any).ObsClient !== 'undefined') {
        // 浏览器环境，使用全局的ObsClient
        const ObsClientConstructor = (window as any).ObsClient;
        this.obsClient = new ObsClientConstructor({
          access_key_id: obsConfig.accessKey,
          secret_access_key: obsConfig.secretKey,
          server: obsConfig.endpoint,
          timeout: obsConfig.timeout || 30000,
          max_retry_count: obsConfig.retryCount || 3
        });
      } else if (typeof global !== 'undefined' && typeof (global as any).ObsClient !== 'undefined') {
        // Node.js环境，使用全局的ObsClient
        const ObsClientConstructor = (global as any).ObsClient;
        this.obsClient = new ObsClientConstructor({
          access_key_id: obsConfig.accessKey,
          secret_access_key: obsConfig.secretKey,
          server: obsConfig.endpoint,
          timeout: obsConfig.timeout || 30000,
          max_retry_count: obsConfig.retryCount || 3
        });
      } else {
        // 如果SDK不可用，创建模拟客户端用于测试
        console.warn('华为云OBS SDK不可用，使用模拟客户端');
        this.obsClient = this.createMockObsClient(obsConfig);
      }

      this.bucketName = obsConfig.bucketName;
      this.config = config;

      // 测试连接
      const testResult = await this.testConnection();
      if (!testResult) {
        throw new Error('连接测试失败');
      }

      this.initialized = true;
      console.log(`华为云OBS提供者初始化成功: ${this.bucketName}`);
    } catch (error) {
      this.initialized = false;
      throw new Error(`华为云OBS初始化失败: ${(error as Error).message}`);
    }
  }
  
  /**
   * 释放资源
   */
  async dispose(): Promise<void> {
    if (this.obsClient) {
      this.obsClient = null;
    }
    this.initialized = false;
    console.log('华为云OBS提供者已释放');
  }
  
  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    if (!this.obsClient) {
      return false;
    }

    try {
      return new Promise((resolve) => {
        this.obsClient!.headBucket({
          Bucket: this.bucketName
        }, (err, result) => {
          if (err) {
            console.error('华为云OBS连接测试失败:', err);
            resolve(false);
          } else if (result.CommonMsg.Status === 200) {
            console.log('华为云OBS连接测试成功');
            resolve(true);
          } else {
            console.error('华为云OBS连接测试失败:', result.CommonMsg);
            resolve(false);
          }
        });
      });
    } catch (error) {
      console.error('华为云OBS连接测试异常:', error);
      return false;
    }
  }
  
  /**
   * 获取对象
   */
  async get(key: string, options?: GetOptions): Promise<StorageResult<any>> {
    this.ensureInitialized();

    if (!this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS客户端未初始化'));
    }

    try {
      const params: any = {
        Bucket: this.bucketName,
        Key: key
      };

      // 处理范围请求
      if (options?.range) {
        params.Range = `bytes=${options.range.start}-${options.range.end}`;
      }

      // 设置响应类型
      if (options?.saveByType) {
        params.SaveByType = options.saveByType;
      }

      return new Promise((resolve) => {
        this.obsClient!.getObject(params, (err, result) => {
          if (err) {
            console.error('获取对象失败:', err);
            resolve(StorageResultFactory.failure(err as Error));
          } else if (result.CommonMsg.Status === 200) {
            resolve(StorageResultFactory.success(result.InterfaceResult.Content, {
              contentType: result.InterfaceResult.ContentType,
              lastModified: result.InterfaceResult.LastModified,
              etag: result.InterfaceResult.ETag
            }));
          } else {
            resolve(StorageResultFactory.failure(new Error(`获取对象失败: ${result.CommonMsg.Code}`)));
          }
        });
      });
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  /**
   * 上传对象
   */
  async put(key: string, data: any, options?: PutOptions): Promise<StorageResult<void>> {
    this.ensureInitialized();

    if (!this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS客户端未初始化'));
    }

    try {
      const params: any = {
        Bucket: this.bucketName,
        Key: key,
        Body: data
      };

      // 设置内容类型
      if (options?.contentType) {
        params.ContentType = options.contentType;
      }

      // 设置元数据
      if (options?.metadata) {
        params.Metadata = options.metadata;
      }

      return new Promise((resolve) => {
        this.obsClient!.putObject(params, (err, result) => {
          if (err) {
            console.error('上传对象失败:', err);
            resolve(StorageResultFactory.failure(err as Error));
          } else if (result.CommonMsg.Status === 200) {
            resolve(StorageResultFactory.success(undefined, {
              etag: result.InterfaceResult.ETag
            }));
          } else {
            resolve(StorageResultFactory.failure(new Error(`上传对象失败: ${result.CommonMsg.Code}`)));
          }
        });
      });
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  /**
   * 删除对象
   */
  async delete(key: string): Promise<StorageResult<void>> {
    this.ensureInitialized();

    if (!this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS客户端未初始化'));
    }

    try {
      return new Promise((resolve) => {
        this.obsClient!.deleteObject({
          Bucket: this.bucketName,
          Key: key
        }, (err, result) => {
          if (err) {
            console.error('删除对象失败:', err);
            resolve(StorageResultFactory.failure(err as Error));
          } else if (result.CommonMsg.Status === 204) {
            resolve(StorageResultFactory.success());
          } else {
            resolve(StorageResultFactory.failure(new Error(`删除对象失败: ${result.CommonMsg.Code}`)));
          }
        });
      });
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  /**
   * 列出对象
   */
  async list(prefix?: string, options?: ListOptions): Promise<StorageResult<string[]>> {
    this.ensureInitialized();

    if (!this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS客户端未初始化'));
    }

    try {
      const params: any = {
        Bucket: this.bucketName,
        MaxKeys: options?.maxKeys || 1000
      };

      if (prefix) {
        params.Prefix = prefix;
      }

      if (options?.marker) {
        params.Marker = options.marker;
      }

      if (options?.delimiter) {
        params.Delimiter = options.delimiter;
      }

      return new Promise((resolve) => {
        this.obsClient!.listObjects(params, (err, result) => {
          if (err) {
            console.error('列出对象失败:', err);
            resolve(StorageResultFactory.failure(err as Error));
          } else if (result.CommonMsg.Status === 200) {
            const keys = result.InterfaceResult.Contents?.map((obj: any) => obj.Key) || [];
            resolve(StorageResultFactory.success(keys));
          } else {
            resolve(StorageResultFactory.failure(new Error(`列出对象失败: ${result.CommonMsg.Code}`)));
          }
        });
      });
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  /**
   * 批量获取对象
   */
  async getBatch(keys: string[], options?: BatchOptions): Promise<StorageResult<Record<string, any>>> {
    const results: Record<string, any> = {};
    const errors: string[] = [];
    const concurrency = options?.concurrency || 5;
    
    // 分批处理以控制并发
    for (let i = 0; i < keys.length; i += concurrency) {
      const batch = keys.slice(i, i + concurrency);
      const promises = batch.map(async (key) => {
        try {
          const result = await this.get(key);
          if (result.success) {
            results[key] = result.data;
          } else {
            errors.push(`${key}: ${result.error?.message}`);
            if (!options?.continueOnError) {
              throw new Error(`批量获取在 ${key} 处停止`);
            }
          }
        } catch (error) {
          errors.push(`${key}: ${(error as Error).message}`);
          if (!options?.continueOnError) {
            throw error;
          }
        }
      });
      
      await Promise.all(promises);
    }

    if (errors.length > 0 && !options?.continueOnError) {
      return StorageResultFactory.failure(new Error(`批量获取失败: ${errors.join(', ')}`), { results });
    }

    return StorageResultFactory.success(results, { errors });
  }

  /**
   * 批量上传对象
   */
  async putBatch(items: Record<string, any>, options?: BatchOptions): Promise<StorageResult<void>> {
    const errors: string[] = [];
    const keys = Object.keys(items);
    const concurrency = options?.concurrency || 5;

    // 分批处理以控制并发
    for (let i = 0; i < keys.length; i += concurrency) {
      const batch = keys.slice(i, i + concurrency);
      const promises = batch.map(async (key) => {
        try {
          const result = await this.put(key, items[key]);
          if (!result.success) {
            errors.push(`${key}: ${result.error?.message}`);
            if (!options?.continueOnError) {
              throw new Error(`批量上传在 ${key} 处停止`);
            }
          }
        } catch (error) {
          errors.push(`${key}: ${(error as Error).message}`);
          if (!options?.continueOnError) {
            throw error;
          }
        }
      });
      
      await Promise.all(promises);
    }

    if (errors.length > 0 && !options?.continueOnError) {
      return StorageResultFactory.failure(new Error(`批量上传失败: ${errors.join(', ')}`));
    }

    return StorageResultFactory.success(undefined, { errors });
  }

  /**
   * 批量删除对象
   */
  async deleteBatch(keys: string[], options?: BatchOptions): Promise<StorageResult<void>> {
    this.ensureInitialized();

    if (!this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS客户端未初始化'));
    }

    try {
      const deleteObjects = keys.map(key => ({ Key: key }));

      return new Promise((resolve) => {
        this.obsClient!.deleteObjects({
          Bucket: this.bucketName,
          Delete: {
            Objects: deleteObjects
          }
        }, (err, result) => {
          if (err) {
            console.error('批量删除失败:', err);
            resolve(StorageResultFactory.failure(err as Error));
          } else if (result.CommonMsg.Status === 200) {
            resolve(StorageResultFactory.success());
          } else {
            resolve(StorageResultFactory.failure(new Error(`批量删除失败: ${result.CommonMsg.Code}`)));
          }
        });
      });
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 获取对象元数据
   */
  async getMetadata(key: string): Promise<StorageResult<ObjectMetadata>> {
    this.ensureInitialized();

    if (!this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS客户端未初始化'));
    }

    try {
      return new Promise((resolve) => {
        this.obsClient!.headObject({
          Bucket: this.bucketName,
          Key: key
        }, (err, result) => {
          if (err) {
            console.error('获取元数据失败:', err);
            resolve(StorageResultFactory.failure(err as Error));
          } else if (result.CommonMsg.Status === 200) {
            const metadata: ObjectMetadata = {
              size: parseInt(result.InterfaceResult.ContentLength),
              lastModified: new Date(result.InterfaceResult.LastModified),
              etag: result.InterfaceResult.ETag,
              contentType: result.InterfaceResult.ContentType,
              customMetadata: result.InterfaceResult.Metadata
            };

            resolve(StorageResultFactory.success(metadata));
          } else {
            resolve(StorageResultFactory.failure(new Error(`获取元数据失败: ${result.CommonMsg.Code}`)));
          }
        });
      });
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 获取对象流 - 支持范围下载和分块下载
   */
  async getStream(key: string, options?: StreamOptions): Promise<ReadableStream> {
    this.ensureInitialized();

    if (!this.obsClient) {
      throw new Error('OBS客户端未初始化');
    }

    try {
      // 获取文件元数据
      const metadataResult = await this.getMetadata(key);
      if (!metadataResult.success) {
        throw new Error(`文件不存在: ${key}`);
      }

      const fileSize = metadataResult.data!.size;
      const chunkSize = options?.chunkSize || 1024 * 1024; // 默认1MB分块

      // 保存this引用
      const self = this;

      // 创建真正的流式下载
      return new ReadableStream({
        async start(controller) {
          try {
            let currentOffset = 0;

            while (currentOffset < fileSize) {
              const endOffset = Math.min(currentOffset + chunkSize - 1, fileSize - 1);

              // 使用范围请求下载分块
              const chunkData = await self.downloadChunk(key, currentOffset, endOffset);
              controller.enqueue(chunkData);

              currentOffset = endOffset + 1;

              // 报告进度
              if (options?.onProgress) {
                const progress: ProgressInfo = {
                  loaded: currentOffset,
                  total: fileSize,
                  percentage: Math.round((currentOffset / fileSize) * 100),
                  speed: 0,
                  remainingTime: 0
                };
                options.onProgress(progress);
              }
            }

            controller.close();
          } catch (error) {
            controller.error(error);
          }
        }
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * 上传对象流 - 真正的流式分块上传
   */
  async putStream(key: string, stream: ReadableStream, options?: PutOptions & StreamOptions): Promise<StorageResult<void>> {
    try {
      const reader = stream.getReader();
      const partSize = options?.partSize || 5 * 1024 * 1024; // 默认5MB分块
      const maxConcurrency = options?.maxConcurrency || 3; // 默认3个并发

      // 检查是否需要使用分块上传
      let totalSize = 0;
      let shouldUseMultipart = false;

      // 先读取一些数据来判断文件大小
      const initialChunks: Uint8Array[] = [];
      let initialSize = 0;

      while (initialSize < partSize) {
        const { done, value } = await reader.read();
        if (done) break;

        initialChunks.push(value);
        initialSize += value.length;
      }

      // 如果还有更多数据，则使用分块上传
      const { done: hasMore } = await reader.read();
      if (!hasMore) {
        // 还有更多数据，使用分块上传
        shouldUseMultipart = true;

        // 重新创建流，包含已读取的数据
        const allChunks = [...initialChunks];
        if (hasMore) {
          // 继续读取剩余数据
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            allChunks.push(value);
          }
        }

        return await this.performMultipartUpload(key, allChunks, options);
      } else {
        // 小文件，使用普通上传
        const totalLength = initialChunks.reduce((sum, chunk) => sum + chunk.length, 0);
        const result = new Uint8Array(totalLength);
        let offset = 0;

        for (const chunk of initialChunks) {
          result.set(chunk, offset);
          offset += chunk.length;
        }

        return await this.put(key, result, options);
      }
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 生成签名URL
   */
  async getSignedUrl(key: string, options?: SignedUrlOptions): Promise<string> {
    this.ensureInitialized();

    if (!this.obsClient) {
      throw new Error('OBS客户端未初始化');
    }

    const params: any = {
      Bucket: this.bucketName,
      Key: key,
      Expires: options?.expires || 3600,
      Method: options?.method || 'GET'
    };

    return this.obsClient.createSignedUrlSync(params);
  }

  /**
   * 初始化分块上传
   */
  async initiateMultipartUpload(key: string, options?: MultipartUploadOptions): Promise<StorageResult<string>> {
    this.ensureInitialized();

    if (!this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS客户端未初始化'));
    }

    try {
      const params: any = {
        Bucket: this.bucketName,
        Key: key
      };

      if (options?.contentType) {
        params.ContentType = options.contentType;
      }

      if (options?.metadata) {
        params.Metadata = options.metadata;
      }

      return new Promise((resolve) => {
        this.obsClient!.initiateMultipartUpload(params, (err, result) => {
          if (err) {
            console.error('初始化分块上传失败:', err);
            resolve(StorageResultFactory.failure(err as Error));
          } else if (result.CommonMsg.Status === 200) {
            resolve(StorageResultFactory.success(result.InterfaceResult.UploadId));
          } else {
            resolve(StorageResultFactory.failure(new Error(`初始化分块上传失败: ${result.CommonMsg.Code}`)));
          }
        });
      });
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 上传分块
   */
  async uploadPart(key: string, uploadId: string, partNumber: number, data: Uint8Array): Promise<StorageResult<UploadPartInfo>> {
    this.ensureInitialized();

    if (!this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS客户端未初始化'));
    }

    try {
      const params: any = {
        Bucket: this.bucketName,
        Key: key,
        PartNumber: partNumber,
        UploadId: uploadId,
        Body: data
      };

      return new Promise((resolve) => {
        this.obsClient!.uploadPart(params, (err, result) => {
          if (err) {
            console.error(`上传分块 ${partNumber} 失败:`, err);
            resolve(StorageResultFactory.failure(err as Error));
          } else if (result.CommonMsg.Status === 200) {
            const partInfo: UploadPartInfo = {
              partNumber,
              etag: result.InterfaceResult.ETag,
              size: data.length
            };
            resolve(StorageResultFactory.success(partInfo));
          } else {
            resolve(StorageResultFactory.failure(new Error(`上传分块失败: ${result.CommonMsg.Code}`)));
          }
        });
      });
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 完成分块上传
   */
  async completeMultipartUpload(key: string, uploadId: string, parts: UploadPartInfo[]): Promise<StorageResult<void>> {
    this.ensureInitialized();

    if (!this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS客户端未初始化'));
    }

    try {
      const params: any = {
        Bucket: this.bucketName,
        Key: key,
        UploadId: uploadId,
        MultipartUpload: {
          Parts: parts.map(part => ({
            PartNumber: part.partNumber,
            ETag: part.etag
          }))
        }
      };

      return new Promise((resolve) => {
        this.obsClient!.completeMultipartUpload(params, (err, result) => {
          if (err) {
            console.error('完成分块上传失败:', err);
            resolve(StorageResultFactory.failure(err as Error));
          } else if (result.CommonMsg.Status === 200) {
            resolve(StorageResultFactory.success());
          } else {
            resolve(StorageResultFactory.failure(new Error(`完成分块上传失败: ${result.CommonMsg.Code}`)));
          }
        });
      });
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 取消分块上传
   */
  async abortMultipartUpload(key: string, uploadId: string): Promise<StorageResult<void>> {
    this.ensureInitialized();

    if (!this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS客户端未初始化'));
    }

    try {
      const params: any = {
        Bucket: this.bucketName,
        Key: key,
        UploadId: uploadId
      };

      const result = await this.withTimeout(
        this.withRetry(() => this.obsClient!.abortMultipartUpload(params))
      );

      if (result.CommonMsg.Status === 200 || result.CommonMsg.Status === 204) {
        return StorageResultFactory.success();
      } else {
        return StorageResultFactory.failure(new Error(`取消分块上传失败: ${result.CommonMsg.Code}`));
      }
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 列出分块上传
   */
  async listMultipartUploads(prefix?: string): Promise<StorageResult<MultipartUploadInfo[]>> {
    this.ensureInitialized();

    if (!this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS客户端未初始化'));
    }

    try {
      const params: any = {
        Bucket: this.bucketName
      };

      if (prefix) {
        params.Prefix = prefix;
      }

      const result = await this.withTimeout(
        this.withRetry(() => this.obsClient!.listMultipartUploads(params))
      );

      if (result.CommonMsg.Status === 200) {
        const uploads: MultipartUploadInfo[] = (result.InterfaceResult.Uploads || []).map((upload: any) => ({
          uploadId: upload.UploadId,
          key: upload.Key,
          bucket: upload.Bucket,
          parts: []
        }));
        return StorageResultFactory.success(uploads);
      } else {
        return StorageResultFactory.failure(new Error(`列出分块上传失败: ${result.CommonMsg.Code}`));
      }
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 获取存储统计信息
   */
  async getStats(): Promise<StorageResult<StorageStats>> {
    this.ensureInitialized();

    try {
      const listResult = await this.list();
      if (!listResult.success) {
        return StorageResultFactory.failure(new Error('无法获取对象列表'));
      }

      const keys = listResult.data || [];
      let totalSize = 0;
      let lastModified = new Date(0);

      // 获取每个对象的元数据来计算总大小
      for (const key of keys.slice(0, 100)) { // 限制为前100个对象以避免过多请求
        const metadataResult = await this.getMetadata(key);
        if (metadataResult.success && metadataResult.data) {
          totalSize += metadataResult.data.size;
          if (metadataResult.data.lastModified > lastModified) {
            lastModified = metadataResult.data.lastModified;
          }
        }
      }

      const stats: StorageStats = {
        totalObjects: keys.length,
        totalSize,
        lastModified,
        provider: this.name
      };

      return StorageResultFactory.success(stats);
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 更新配置
   */
  async updateConfig(config: Partial<IStorageConfig>): Promise<void> {
    const newConfig = { ...this.config, ...config };
    await this.dispose();
    await this.initialize(newConfig);
  }

  /**
   * 验证配置参数
   */
  private validateConfig(config: ICloudStorageConfig): void {
    if (!config.accessKey) {
      throw new Error('accessKey不能为空');
    }
    if (!config.secretKey) {
      throw new Error('secretKey不能为空');
    }
    if (!config.endpoint) {
      throw new Error('endpoint不能为空');
    }
    if (!config.bucketName) {
      throw new Error('bucketName不能为空');
    }
  }

  /**
   * 执行分块上传
   */
  private async performMultipartUpload(key: string, chunks: Uint8Array[], options?: PutOptions & StreamOptions): Promise<StorageResult<void>> {
    try {
      const partSize = options?.partSize || 5 * 1024 * 1024; // 5MB
      const maxConcurrency = options?.maxConcurrency || 3;

      // 合并所有数据块
      const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
      const allData = new Uint8Array(totalLength);
      let offset = 0;

      for (const chunk of chunks) {
        allData.set(chunk, offset);
        offset += chunk.length;
      }

      // 初始化分块上传
      const uploadResult = await this.initiateMultipartUpload(key, {
        partSize,
        maxConcurrency,
        contentType: options?.contentType,
        metadata: options?.metadata,
        onProgress: options?.onProgress
      });

      if (!uploadResult.success) {
        return uploadResult;
      }

      const uploadId = uploadResult.data!;
      const parts: UploadPartInfo[] = [];

      try {
        // 分块上传
        const totalParts = Math.ceil(totalLength / partSize);
        const uploadPromises: Promise<void>[] = [];
        let currentConcurrency = 0;

        for (let partNumber = 1; partNumber <= totalParts; partNumber++) {
          const start = (partNumber - 1) * partSize;
          const end = Math.min(start + partSize, totalLength);
          const partData = allData.slice(start, end);

          // 控制并发数
          while (currentConcurrency >= maxConcurrency) {
            await Promise.race(uploadPromises);
          }

          currentConcurrency++;
          const uploadPromise = this.uploadPart(key, uploadId, partNumber, partData)
            .then(result => {
              currentConcurrency--;
              if (result.success) {
                parts.push(result.data!);

                // 报告进度
                if (options?.onProgress) {
                  const progress: ProgressInfo = {
                    loaded: parts.length * partSize,
                    total: totalLength,
                    percentage: Math.round((parts.length / totalParts) * 100),
                    currentPart: partNumber,
                    totalParts
                  };
                  options.onProgress(progress);
                }
              } else {
                throw result.error;
              }
            });

          uploadPromises.push(uploadPromise);
        }

        // 等待所有分块上传完成
        await Promise.all(uploadPromises);

        // 按分块号排序
        parts.sort((a, b) => a.partNumber - b.partNumber);

        // 完成分块上传
        const completeResult = await this.completeMultipartUpload(key, uploadId, parts);
        return completeResult;

      } catch (error) {
        // 上传失败，取消分块上传
        await this.abortMultipartUpload(key, uploadId);
        throw error;
      }

    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 下载分块数据（范围请求）
   */
  private async downloadChunk(key: string, startByte: number, endByte: number): Promise<Uint8Array> {
    this.ensureInitialized();

    if (!this.obsClient) {
      throw new Error('OBS客户端未初始化');
    }

    try {
      const params: any = {
        Bucket: this.bucketName,
        Key: key,
        Range: `bytes=${startByte}-${endByte}`
      };

      return new Promise((resolve, reject) => {
        this.obsClient!.getObject(params, (err, result) => {
          if (err) {
            console.error(`下载分块 ${startByte}-${endByte} 失败:`, err);
            reject(err);
          } else if (result.CommonMsg.Status === 200 || result.CommonMsg.Status === 206) {
            // 处理返回的数据
            let data: Uint8Array;
            if (result.InterfaceResult.Content instanceof ArrayBuffer) {
              data = new Uint8Array(result.InterfaceResult.Content);
            } else if (typeof result.InterfaceResult.Content === 'string') {
              const encoder = new TextEncoder();
              data = encoder.encode(result.InterfaceResult.Content);
            } else if (result.InterfaceResult.Content instanceof Uint8Array) {
              data = result.InterfaceResult.Content;
            } else {
              // 尝试转换为字符串再编码
              const encoder = new TextEncoder();
              data = encoder.encode(String(result.InterfaceResult.Content));
            }

            resolve(data);
          } else {
            reject(new Error(`下载分块失败: ${result.CommonMsg.Code}`));
          }
        });
      });
    } catch (error) {
      throw new Error(`下载分块失败: ${(error as Error).message}`);
    }
  }

  /**
   * 验证配置参数
   */
  private validateConfig(config: ICloudStorageConfig): void {
    if (!config.accessKey) {
      throw new Error('accessKey不能为空');
    }
    if (!config.secretKey) {
      throw new Error('secretKey不能为空');
    }
    if (!config.endpoint) {
      throw new Error('endpoint不能为空');
    }
    if (!config.bucketName) {
      throw new Error('bucketName不能为空');
    }
  }

  /**
   * 创建模拟OBS客户端（用于测试环境）
   */
  private createMockObsClient(config: ICloudStorageConfig): ObsClient {
    const mockData = new Map<string, any>();

    return {
      headBucket: (params: any, callback?: Function) => {
        setTimeout(() => {
          if (callback) {
            callback(null, {
              CommonMsg: { Status: 200 },
              InterfaceResult: {}
            });
          }
        }, 10);
      },

      getObject: (params: any, callback?: Function) => {
        setTimeout(() => {
          const data = mockData.get(params.Key);
          if (callback) {
            if (data) {
              callback(null, {
                CommonMsg: { Status: 200 },
                InterfaceResult: {
                  Content: data,
                  ContentType: 'application/json',
                  LastModified: new Date().toISOString(),
                  ETag: `mock-etag-${params.Key}`
                }
              });
            } else {
              callback(new Error('对象不存在'));
            }
          }
        }, 10);
      },

      putObject: (params: any, callback?: Function) => {
        setTimeout(() => {
          mockData.set(params.Key, params.Body);
          if (callback) {
            callback(null, {
              CommonMsg: { Status: 200 },
              InterfaceResult: {
                ETag: `mock-etag-${Date.now()}`
              }
            });
          }
        }, 10);
      },

      deleteObject: (params: any, callback?: Function) => {
        setTimeout(() => {
          mockData.delete(params.Key);
          if (callback) {
            callback(null, {
              CommonMsg: { Status: 204 },
              InterfaceResult: {}
            });
          }
        }, 10);
      },

      deleteObjects: (params: any, callback?: Function) => {
        setTimeout(() => {
          params.Delete.Objects.forEach((obj: any) => mockData.delete(obj.Key));
          if (callback) {
            callback(null, {
              CommonMsg: { Status: 200 },
              InterfaceResult: {}
            });
          }
        }, 10);
      },

      listObjects: (params: any, callback?: Function) => {
        setTimeout(() => {
          const keys = Array.from(mockData.keys());
          const filteredKeys = params.Prefix ? keys.filter(key => key.startsWith(params.Prefix)) : keys;

          if (callback) {
            callback(null, {
              CommonMsg: { Status: 200 },
              InterfaceResult: {
                Contents: filteredKeys.map(key => ({
                  Key: key,
                  Size: JSON.stringify(mockData.get(key)).length,
                  LastModified: new Date().toISOString()
                }))
              }
            });
          }
        }, 10);
      },

      headObject: (params: any, callback?: Function) => {
        setTimeout(() => {
          const data = mockData.get(params.Key);
          if (callback) {
            if (data) {
              callback(null, {
                CommonMsg: { Status: 200 },
                InterfaceResult: {
                  ContentLength: JSON.stringify(data).length.toString(),
                  LastModified: new Date().toISOString(),
                  ETag: `mock-etag-${params.Key}`,
                  ContentType: 'application/json',
                  Metadata: {}
                }
              });
            } else {
              callback(new Error('对象不存在'));
            }
          }
        }, 10);
      },

      initiateMultipartUpload: (params: any, callback?: Function) => {
        setTimeout(() => {
          const uploadId = `mock-upload-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
          if (callback) {
            callback(null, {
              CommonMsg: { Status: 200 },
              InterfaceResult: {
                UploadId: uploadId
              }
            });
          }
        }, 10);
      },

      uploadPart: (params: any, callback?: Function) => {
        setTimeout(() => {
          if (callback) {
            callback(null, {
              CommonMsg: { Status: 200 },
              InterfaceResult: {
                ETag: `mock-part-etag-${params.PartNumber}-${Date.now()}`
              }
            });
          }
        }, 50);
      },

      completeMultipartUpload: (params: any, callback?: Function) => {
        setTimeout(() => {
          // 模拟完成分块上传，将数据存储到mockData
          const combinedData = `multipart-data-${params.Key}-${Date.now()}`;
          mockData.set(params.Key, combinedData);

          if (callback) {
            callback(null, {
              CommonMsg: { Status: 200 },
              InterfaceResult: {
                ETag: `mock-complete-etag-${Date.now()}`
              }
            });
          }
        }, 100);
      },

      abortMultipartUpload: (params: any, callback?: Function) => {
        setTimeout(() => {
          if (callback) {
            callback(null, {
              CommonMsg: { Status: 204 },
              InterfaceResult: {}
            });
          }
        }, 10);
      },

      listMultipartUploads: (params: any, callback?: Function) => {
        setTimeout(() => {
          if (callback) {
            callback(null, {
              CommonMsg: { Status: 200 },
              InterfaceResult: {
                Uploads: []
              }
            });
          }
        }, 10);
      }
    };
  }
}
