// AWS S3 SDK类型定义（用于MinIO兼容）
interface S3Client {
  headBucket(params: any): Promise<any>;
  getObject(params: any): Promise<any>;
  putObject(params: any): Promise<any>;
  deleteObject(params: any): Promise<any>;
  deleteObjects(params: any): Promise<any>;
  listObjectsV2(params: any): Promise<any>;
  headObject(params: any): Promise<any>;
  createMultipartUpload(params: any): Promise<any>;
  uploadPart(params: any): Promise<any>;
  completeMultipartUpload(params: any): Promise<any>;
  abortMultipartUpload(params: any): Promise<any>;
  listMultipartUploads(params: any): Promise<any>;
}

// 声明全局的AWS SDK
declare global {
  const AWS: {
    S3: {
      new (config: {
        endpoint: string;
        accessKeyId: string;
        secretAccessKey: string;
        s3ForcePathStyle: boolean;
        signatureVersion: string;
        region?: string;
      }): S3Client;
    };
  };
}
import { BaseStorageProvider } from '../interfaces/IStorageProvider';
import { StorageType } from '../enums/StorageType';
import { IStorageConfig, ICloudStorageConfig } from '../types/StorageConfig';
import {
  StorageResult,
  StorageResultFactory,
  ObjectMetadata,
  GetOptions,
  PutOptions,
  ListOptions,
  SignedUrlOptions,
  BatchOptions,
  StreamOptions,
  StorageStats,
  OperationResultFactory,
  MultipartUploadOptions,
  MultipartUploadInfo,
  UploadPartInfo,
  ProgressInfo
} from '../types/StorageResult';

/**
 * MinIO存储提供者
 * 实现IStorageProvider接口，提供MinIO对象存储服务
 * 使用AWS S3 SDK实现S3兼容的MinIO访问
 */
export class MinioProvider extends BaseStorageProvider {
  private s3Client: S3Client | null = null;
  private bucketName: string = '';

  constructor() {
    super('MinIO对象存储', StorageType.MINIO);
  }
  
  /**
   * 初始化MinIO客户端（使用AWS S3 SDK）
   */
  async initialize(config: IStorageConfig): Promise<void> {
    try {
      const minioConfig = config as ICloudStorageConfig;

      // 验证配置参数
      this.validateConfig(minioConfig);

      // 检查AWS SDK是否可用
      if (typeof window !== 'undefined' && typeof (window as any).AWS !== 'undefined') {
        // 浏览器环境，使用全局的AWS SDK
        const AWS = (window as any).AWS;
        this.s3Client = new AWS.S3({
          endpoint: minioConfig.endpoint,
          accessKeyId: minioConfig.accessKey,
          secretAccessKey: minioConfig.secretKey,
          s3ForcePathStyle: true, // MinIO需要路径风格
          signatureVersion: 'v4',
          region: minioConfig.region || 'us-east-1'
        });
      } else if (typeof global !== 'undefined' && typeof (global as any).AWS !== 'undefined') {
        // Node.js环境，使用全局的AWS SDK
        const AWS = (global as any).AWS;
        this.s3Client = new AWS.S3({
          endpoint: minioConfig.endpoint,
          accessKeyId: minioConfig.accessKey,
          secretAccessKey: minioConfig.secretKey,
          s3ForcePathStyle: true,
          signatureVersion: 'v4',
          region: minioConfig.region || 'us-east-1'
        });
      } else {
        // 如果AWS SDK不可用，创建模拟客户端用于测试
        console.warn('AWS SDK不可用，使用模拟客户端');
        this.s3Client = this.createMockS3Client(minioConfig);
      }

      this.bucketName = minioConfig.bucketName;
      this.config = config;

      // 测试连接
      const testResult = await this.testConnection();
      if (!testResult) {
        throw new Error('连接测试失败');
      }

      this.initialized = true;
      console.log(`MinIO提供者初始化成功: ${this.bucketName}`);
    } catch (error) {
      this.initialized = false;
      throw new Error(`MinIO初始化失败: ${(error as Error).message}`);
    }
  }
  
  /**
   * 释放资源
   */
  async dispose(): Promise<void> {
    if (this.s3Client) {
      this.s3Client = null;
    }
    this.initialized = false;
    console.log('MinIO提供者已释放');
  }

  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    if (!this.s3Client) {
      return false;
    }

    try {
      await this.s3Client.headBucket({
        Bucket: this.bucketName
      });
      return true;
    } catch (error) {
      console.error('MinIO连接测试失败:', error);
      return false;
    }
  }
  
  /**
   * 获取对象
   */
  async get(key: string, options?: GetOptions): Promise<StorageResult<any>> {
    this.ensureInitialized();

    if (!this.s3Client) {
      return StorageResultFactory.failure(new Error('S3客户端未初始化'));
    }

    try {
      const params: any = {
        Bucket: this.bucketName,
        Key: key
      };

      // 处理范围请求
      if (options?.range) {
        params.Range = `bytes=${options.range.start}-${options.range.end}`;
      }

      const result = await this.withTimeout(
        this.withRetry(() => this.s3Client!.getObject(params))
      );

      let data: any = result.Body;

      // 根据saveByType处理数据
      if (options?.saveByType === 'text') {
        if (data instanceof ArrayBuffer) {
          data = new TextDecoder().decode(data);
        } else if (data instanceof Uint8Array) {
          data = new TextDecoder().decode(data);
        } else if (typeof data === 'string') {
          // 已经是字符串
        } else {
          data = String(data);
        }
      } else if (options?.saveByType === 'arraybuffer') {
        if (data instanceof ArrayBuffer) {
          // 已经是ArrayBuffer
        } else if (data instanceof Uint8Array) {
          data = data.buffer.slice(data.byteOffset, data.byteOffset + data.byteLength);
        } else {
          const encoder = new TextEncoder();
          data = encoder.encode(String(data)).buffer;
        }
      } else if (options?.saveByType === 'blob') {
        data = new Blob([data]);
      }

      return StorageResultFactory.success(data, {
        contentType: result.ContentType,
        lastModified: result.LastModified,
        etag: result.ETag
      });
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  /**
   * 上传对象
   */
  async put(key: string, data: any, options?: PutOptions): Promise<StorageResult<void>> {
    this.ensureInitialized();

    if (!this.s3Client) {
      return StorageResultFactory.failure(new Error('S3客户端未初始化'));
    }

    try {
      const params: any = {
        Bucket: this.bucketName,
        Key: key,
        Body: data
      };

      if (options?.contentType) {
        params.ContentType = options.contentType;
      }

      if (options?.metadata) {
        params.Metadata = options.metadata;
      }

      const result = await this.withTimeout(
        this.withRetry(() => this.s3Client!.putObject(params))
      );

      return StorageResultFactory.success(undefined, { etag: result.ETag });
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  /**
   * 删除对象
   */
  async delete(key: string): Promise<StorageResult<void>> {
    this.ensureInitialized();

    if (!this.s3Client) {
      return StorageResultFactory.failure(new Error('S3客户端未初始化'));
    }

    try {
      await this.withTimeout(
        this.withRetry(() => this.s3Client!.deleteObject({
          Bucket: this.bucketName,
          Key: key
        }))
      );
      return StorageResultFactory.success();
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  /**
   * 列出对象
   */
  async list(prefix?: string, options?: ListOptions): Promise<StorageResult<string[]>> {
    this.ensureInitialized();

    if (!this.s3Client) {
      return StorageResultFactory.failure(new Error('S3客户端未初始化'));
    }

    try {
      const params: any = {
        Bucket: this.bucketName,
        MaxKeys: options?.maxKeys || 1000
      };

      if (prefix) {
        params.Prefix = prefix;
      }

      if (options?.marker) {
        params.ContinuationToken = options.marker;
      }

      if (options?.delimiter) {
        params.Delimiter = options.delimiter;
      }

      const result = await this.withTimeout(
        this.withRetry(() => this.s3Client!.listObjectsV2(params))
      );

      const keys = result.Contents?.map((obj: any) => obj.Key) || [];
      return StorageResultFactory.success(keys);
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  /**
   * 批量获取对象
   */
  async getBatch(keys: string[], options?: BatchOptions): Promise<StorageResult<Record<string, any>>> {
    const results: Record<string, any> = {};
    const errors: string[] = [];
    const concurrency = options?.concurrency || 5;
    
    // 分批处理以控制并发
    for (let i = 0; i < keys.length; i += concurrency) {
      const batch = keys.slice(i, i + concurrency);
      const promises = batch.map(async (key) => {
        try {
          const result = await this.get(key);
          if (result.success) {
            results[key] = result.data;
          } else {
            errors.push(`${key}: ${result.error?.message}`);
            if (!options?.continueOnError) {
              throw new Error(`批量获取在 ${key} 处停止`);
            }
          }
        } catch (error) {
          errors.push(`${key}: ${(error as Error).message}`);
          if (!options?.continueOnError) {
            throw error;
          }
        }
      });
      
      await Promise.all(promises);
    }

    if (errors.length > 0 && !options?.continueOnError) {
      return StorageResultFactory.failure(new Error(`批量获取失败: ${errors.join(', ')}`), { results });
    }

    return StorageResultFactory.success(results, { errors });
  }

  /**
   * 批量上传对象
   */
  async putBatch(items: Record<string, any>, options?: BatchOptions): Promise<StorageResult<void>> {
    const errors: string[] = [];
    const keys = Object.keys(items);
    const concurrency = options?.concurrency || 5;

    // 分批处理以控制并发
    for (let i = 0; i < keys.length; i += concurrency) {
      const batch = keys.slice(i, i + concurrency);
      const promises = batch.map(async (key) => {
        try {
          const result = await this.put(key, items[key]);
          if (!result.success) {
            errors.push(`${key}: ${result.error?.message}`);
            if (!options?.continueOnError) {
              throw new Error(`批量上传在 ${key} 处停止`);
            }
          }
        } catch (error) {
          errors.push(`${key}: ${(error as Error).message}`);
          if (!options?.continueOnError) {
            throw error;
          }
        }
      });
      
      await Promise.all(promises);
    }

    if (errors.length > 0 && !options?.continueOnError) {
      return StorageResultFactory.failure(new Error(`批量上传失败: ${errors.join(', ')}`));
    }

    return StorageResultFactory.success(undefined, { errors });
  }

  /**
   * 批量删除对象
   */
  async deleteBatch(keys: string[], options?: BatchOptions): Promise<StorageResult<void>> {
    this.ensureInitialized();

    if (!this.s3Client) {
      return StorageResultFactory.failure(new Error('S3客户端未初始化'));
    }

    try {
      const deleteObjects = keys.map(key => ({ Key: key }));

      await this.withTimeout(
        this.withRetry(() => this.s3Client!.deleteObjects({
          Bucket: this.bucketName,
          Delete: {
            Objects: deleteObjects
          }
        }))
      );
      return StorageResultFactory.success();
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 获取对象元数据
   */
  async getMetadata(key: string): Promise<StorageResult<ObjectMetadata>> {
    this.ensureInitialized();

    if (!this.s3Client) {
      return StorageResultFactory.failure(new Error('S3客户端未初始化'));
    }

    try {
      const result = await this.withTimeout(
        this.withRetry(() => this.s3Client!.headObject({
          Bucket: this.bucketName,
          Key: key
        }))
      );

      const metadata: ObjectMetadata = {
        size: parseInt(result.ContentLength || '0'),
        lastModified: result.LastModified || new Date(),
        etag: result.ETag || '',
        contentType: result.ContentType || 'application/octet-stream',
        customMetadata: result.Metadata || {}
      };

      return StorageResultFactory.success(metadata);
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 获取对象流 - 支持范围下载和分块下载
   */
  async getStream(key: string, options?: StreamOptions): Promise<ReadableStream> {
    this.ensureInitialized();

    if (!this.s3Client) {
      throw new Error('S3客户端未初始化');
    }

    try {
      // 获取文件元数据
      const metadataResult = await this.getMetadata(key);
      if (!metadataResult.success) {
        throw new Error(`文件不存在: ${key}`);
      }

      const fileSize = metadataResult.data!.size;
      const chunkSize = options?.chunkSize || 1024 * 1024; // 默认1MB分块

      // 保存this引用
      const self = this;

      // 创建真正的流式下载
      return new ReadableStream({
        async start(controller) {
          try {
            let currentOffset = 0;

            while (currentOffset < fileSize) {
              const endOffset = Math.min(currentOffset + chunkSize - 1, fileSize - 1);

              // 使用范围请求下载分块
              const chunkData = await self.downloadChunk(key, currentOffset, endOffset);
              controller.enqueue(chunkData);

              currentOffset = endOffset + 1;

              // 报告进度
              if (options?.onProgress) {
                const progress: ProgressInfo = {
                  loaded: currentOffset,
                  total: fileSize,
                  percentage: Math.round((currentOffset / fileSize) * 100),
                  speed: 0, // 可以计算实际速度
                  remainingTime: 0 // 可以计算剩余时间
                };
                options.onProgress(progress);
              }
            }

            controller.close();
          } catch (error) {
            controller.error(error);
          }
        }
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * 上传对象流 - 真正的流式分块上传
   */
  async putStream(key: string, stream: ReadableStream, options?: PutOptions & StreamOptions): Promise<StorageResult<void>> {
    try {
      const reader = stream.getReader();
      const partSize = options?.partSize || 5 * 1024 * 1024; // 默认5MB分块
      const maxConcurrency = options?.maxConcurrency || 3; // 默认3个并发

      // 检查是否需要使用分块上传
      let totalSize = 0;
      let buffer = new Uint8Array(0);
      let shouldUseMultipart = false;

      // 先读取一些数据来判断文件大小
      const initialChunks: Uint8Array[] = [];
      let initialSize = 0;

      while (initialSize < partSize) {
        const { done, value } = await reader.read();
        if (done) break;

        initialChunks.push(value);
        initialSize += value.length;
      }

      // 如果还有更多数据，则使用分块上传
      const { done: hasMore } = await reader.read();
      if (!hasMore) {
        // 还有更多数据，使用分块上传
        shouldUseMultipart = true;

        // 重新创建流，包含已读取的数据
        const allChunks = [...initialChunks];
        if (hasMore) {
          // 继续读取剩余数据
          while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            allChunks.push(value);
          }
        }

        return await this.performMultipartUpload(key, allChunks, options);
      } else {
        // 小文件，使用普通上传
        const totalLength = initialChunks.reduce((sum, chunk) => sum + chunk.length, 0);
        const result = new Uint8Array(totalLength);
        let offset = 0;

        for (const chunk of initialChunks) {
          result.set(chunk, offset);
          offset += chunk.length;
        }

        return await this.put(key, result, options);
      }
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 生成签名URL
   */
  async getSignedUrl(key: string, options?: SignedUrlOptions): Promise<string> {
    this.ensureInitialized();

    if (!this.s3Client) {
      throw new Error('S3客户端未初始化');
    }

    const expires = options?.expires || 3600;
    const method = options?.method || 'GET';

    // 注意：这里需要使用AWS SDK的getSignedUrl方法
    // 由于我们使用的是模拟实现，这里简化处理
    const signedUrl = `${this.config?.endpoint}/${this.bucketName}/${key}?X-Amz-Expires=${expires}&X-Amz-Method=${method}`;
    return signedUrl;
  }

  /**
   * 初始化分块上传
   */
  async initiateMultipartUpload(key: string, options?: MultipartUploadOptions): Promise<StorageResult<string>> {
    this.ensureInitialized();

    if (!this.s3Client) {
      return StorageResultFactory.failure(new Error('S3客户端未初始化'));
    }

    try {
      const params: any = {
        Bucket: this.bucketName,
        Key: key
      };

      if (options?.contentType) {
        params.ContentType = options.contentType;
      }

      if (options?.metadata) {
        params.Metadata = options.metadata;
      }

      const result = await this.withTimeout(
        this.withRetry(() => this.s3Client!.createMultipartUpload(params))
      );

      return StorageResultFactory.success(result.UploadId);
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 上传分块
   */
  async uploadPart(key: string, uploadId: string, partNumber: number, data: Uint8Array): Promise<StorageResult<UploadPartInfo>> {
    this.ensureInitialized();

    if (!this.s3Client) {
      return StorageResultFactory.failure(new Error('S3客户端未初始化'));
    }

    try {
      const result = await this.withTimeout(
        this.withRetry(() => this.s3Client!.uploadPart({
          Bucket: this.bucketName,
          Key: key,
          PartNumber: partNumber,
          UploadId: uploadId,
          Body: data
        }))
      );

      const partInfo: UploadPartInfo = {
        partNumber,
        etag: result.ETag,
        size: data.length
      };

      return StorageResultFactory.success(partInfo);
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 完成分块上传
   */
  async completeMultipartUpload(key: string, uploadId: string, parts: UploadPartInfo[]): Promise<StorageResult<void>> {
    this.ensureInitialized();

    if (!this.s3Client) {
      return StorageResultFactory.failure(new Error('S3客户端未初始化'));
    }

    try {
      const multipartUpload = {
        Parts: parts.map(part => ({
          ETag: part.etag,
          PartNumber: part.partNumber
        }))
      };

      await this.withTimeout(
        this.withRetry(() => this.s3Client!.completeMultipartUpload({
          Bucket: this.bucketName,
          Key: key,
          UploadId: uploadId,
          MultipartUpload: multipartUpload
        }))
      );

      return StorageResultFactory.success();
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 取消分块上传
   */
  async abortMultipartUpload(key: string, uploadId: string): Promise<StorageResult<void>> {
    this.ensureInitialized();

    if (!this.s3Client) {
      return StorageResultFactory.failure(new Error('S3客户端未初始化'));
    }

    try {
      await this.withTimeout(
        this.withRetry(() => this.s3Client!.abortMultipartUpload({
          Bucket: this.bucketName,
          Key: key,
          UploadId: uploadId
        }))
      );

      return StorageResultFactory.success();
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 列出分块上传
   */
  async listMultipartUploads(prefix?: string): Promise<StorageResult<MultipartUploadInfo[]>> {
    this.ensureInitialized();

    if (!this.minioClient) {
      return StorageResultFactory.failure(new Error('MinIO客户端未初始化'));
    }

    try {
      // 在实际实现中，这里应该调用MinIO的list multipart uploads API
      const uploads: MultipartUploadInfo[] = [];

      return StorageResultFactory.success(uploads);
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 获取存储统计信息
   */
  async getStats(): Promise<StorageResult<StorageStats>> {
    this.ensureInitialized();

    try {
      const listResult = await this.list();
      if (!listResult.success) {
        return StorageResultFactory.failure(new Error('无法获取对象列表'));
      }

      const keys = listResult.data || [];
      let totalSize = 0;
      let lastModified = new Date(0);

      // 获取每个对象的元数据来计算总大小
      for (const key of keys.slice(0, 100)) { // 限制为前100个对象以避免过多请求
        const metadataResult = await this.getMetadata(key);
        if (metadataResult.success && metadataResult.data) {
          totalSize += metadataResult.data.size;
          if (metadataResult.data.lastModified > lastModified) {
            lastModified = metadataResult.data.lastModified;
          }
        }
      }

      const stats: StorageStats = {
        totalObjects: keys.length,
        totalSize,
        lastModified,
        provider: this.name
      };

      return StorageResultFactory.success(stats);
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 更新配置
   */
  async updateConfig(config: Partial<IStorageConfig>): Promise<void> {
    const newConfig = { ...this.config, ...config };
    await this.dispose();
    await this.initialize(newConfig);
  }

  /**
   * 确保bucket存在
   */
  private async ensureBucketExists(): Promise<void> {
    if (!this.minioClient) {
      throw new Error('MinIO客户端未初始化');
    }

    const exists = await this.minioClient.bucketExists(this.bucketName);
    if (!exists) {
      await this.minioClient.makeBucket(this.bucketName);
      console.log(`已创建bucket: ${this.bucketName}`);
    }
  }

  /**
   * 验证配置参数
   */
  private validateConfig(config: ICloudStorageConfig): void {
    if (!config.accessKey) {
      throw new Error('accessKey不能为空');
    }
    if (!config.secretKey) {
      throw new Error('secretKey不能为空');
    }
    if (!config.endpoint) {
      throw new Error('endpoint不能为空');
    }
    if (!config.bucketName) {
      throw new Error('bucketName不能为空');
    }
  }

  /**
   * 创建模拟MinIO客户端（用于浏览器环境测试）
   */
  private createMockMinioClient(config: MinioConfig): MinioClient {
    const mockData = new Map<string, any>();

    return {
      async bucketExists(bucketName: string): Promise<boolean> {
        return true; // 模拟bucket总是存在
      },

      async makeBucket(bucketName: string, region?: string): Promise<void> {
        console.log(`模拟创建bucket: ${bucketName}`);
      },

      async getObject(bucketName: string, objectName: string): Promise<any> {
        const data = mockData.get(objectName);
        if (!data) {
          throw new Error('对象不存在');
        }

        // 模拟流对象
        return {
          on: (event: string, callback: Function) => {
            if (event === 'data') {
              setTimeout(() => callback(Buffer.from(data)), 10);
            } else if (event === 'end') {
              setTimeout(() => callback(), 20);
            }
          }
        };
      },

      async putObject(bucketName: string, objectName: string, stream: any, size?: number, metaData?: any): Promise<any> {
        mockData.set(objectName, stream);
        return { etag: `mock-etag-${Date.now()}` };
      },

      async removeObject(bucketName: string, objectName: string): Promise<void> {
        mockData.delete(objectName);
      },

      async removeObjects(bucketName: string, objectsList: string[]): Promise<void> {
        objectsList.forEach(key => mockData.delete(key));
      },

      listObjects(bucketName: string, prefix?: string, recursive?: boolean): any {
        const keys = Array.from(mockData.keys());
        const filteredKeys = prefix ? keys.filter(key => key.startsWith(prefix)) : keys;

        return {
          on: (event: string, callback: Function) => {
            if (event === 'data') {
              filteredKeys.forEach(key => {
                setTimeout(() => callback({ name: key }), 10);
              });
            } else if (event === 'end') {
              setTimeout(() => callback(), 20);
            }
          }
        };
      },

      async statObject(bucketName: string, objectName: string): Promise<any> {
        const data = mockData.get(objectName);
        if (!data) {
          throw new Error('对象不存在');
        }

        return {
          size: JSON.stringify(data).length,
          lastModified: new Date(),
          etag: `mock-etag-${objectName}`,
          metaData: { 'content-type': 'application/json' }
        };
      },

      async presignedGetObject(bucketName: string, objectName: string, expiry?: number): Promise<string> {
        return `https://mock-minio.example.com/${bucketName}/${objectName}?X-Amz-Expires=${expiry || 3600}`;
      },

      async presignedPutObject(bucketName: string, objectName: string, expiry?: number): Promise<string> {
        return `https://mock-minio.example.com/${bucketName}/${objectName}?X-Amz-Expires=${expiry || 3600}&X-Amz-Method=PUT`;
      }
    };
  }

  /**
   * 添加超时控制
   */
  protected async withTimeout<T>(promise: Promise<T>, timeoutMs?: number): Promise<T> {
    const timeout = timeoutMs || this.config?.timeout || 30000;

    return Promise.race([
      promise,
      new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error(`操作超时: ${timeout}ms`)), timeout);
      })
    ]);
  }

  /**
   * 执行分块上传
   */
  private async performMultipartUpload(key: string, chunks: Uint8Array[], options?: PutOptions & StreamOptions): Promise<StorageResult<void>> {
    try {
      const partSize = options?.partSize || 5 * 1024 * 1024; // 5MB
      const maxConcurrency = options?.maxConcurrency || 3;

      // 合并所有数据块
      const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
      const allData = new Uint8Array(totalLength);
      let offset = 0;

      for (const chunk of chunks) {
        allData.set(chunk, offset);
        offset += chunk.length;
      }

      // 初始化分块上传
      const uploadResult = await this.initiateMultipartUpload(key, {
        partSize,
        maxConcurrency,
        contentType: options?.contentType,
        metadata: options?.metadata,
        onProgress: options?.onProgress
      });

      if (!uploadResult.success) {
        return uploadResult;
      }

      const uploadId = uploadResult.data!;
      const parts: UploadPartInfo[] = [];

      try {
        // 分块上传
        const totalParts = Math.ceil(totalLength / partSize);
        const uploadPromises: Promise<void>[] = [];
        let currentConcurrency = 0;

        for (let partNumber = 1; partNumber <= totalParts; partNumber++) {
          const start = (partNumber - 1) * partSize;
          const end = Math.min(start + partSize, totalLength);
          const partData = allData.slice(start, end);

          // 控制并发数
          while (currentConcurrency >= maxConcurrency) {
            await Promise.race(uploadPromises);
          }

          currentConcurrency++;
          const uploadPromise = this.uploadPart(key, uploadId, partNumber, partData)
            .then(result => {
              currentConcurrency--;
              if (result.success) {
                parts.push(result.data!);

                // 报告进度
                if (options?.onProgress) {
                  const progress: ProgressInfo = {
                    loaded: parts.length * partSize,
                    total: totalLength,
                    percentage: Math.round((parts.length / totalParts) * 100),
                    currentPart: partNumber,
                    totalParts
                  };
                  options.onProgress(progress);
                }
              } else {
                throw result.error;
              }
            });

          uploadPromises.push(uploadPromise);
        }

        // 等待所有分块上传完成
        await Promise.all(uploadPromises);

        // 按分块号排序
        parts.sort((a, b) => a.partNumber - b.partNumber);

        // 完成分块上传
        const completeResult = await this.completeMultipartUpload(key, uploadId, parts);
        return completeResult;

      } catch (error) {
        // 上传失败，取消分块上传
        await this.abortMultipartUpload(key, uploadId);
        throw error;
      }

    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 下载分块数据（范围请求）
   */
  private async downloadChunk(key: string, startByte: number, endByte: number): Promise<Uint8Array> {
    // 在实际实现中，这里应该使用MinIO的范围请求功能
    // 目前使用模拟实现

    try {
      // 获取完整对象
      const result = await this.get(key);
      if (!result.success) {
        throw new Error(`获取对象失败: ${result.error?.message}`);
      }

      let data: Uint8Array;
      if (typeof result.data === 'string') {
        const encoder = new TextEncoder();
        data = encoder.encode(result.data);
      } else if (result.data instanceof ArrayBuffer) {
        data = new Uint8Array(result.data);
      } else if (result.data instanceof Uint8Array) {
        data = result.data;
      } else {
        const encoder = new TextEncoder();
        data = encoder.encode(JSON.stringify(result.data));
      }

      // 返回指定范围的数据
      return data.slice(startByte, endByte + 1);

    } catch (error) {
      throw new Error(`下载分块失败: ${(error as Error).message}`);
    }
  }

  /**
   * 验证配置参数
   */
  private validateConfig(config: ICloudStorageConfig): void {
    if (!config.accessKey) {
      throw new Error('accessKey不能为空');
    }
    if (!config.secretKey) {
      throw new Error('secretKey不能为空');
    }
    if (!config.endpoint) {
      throw new Error('endpoint不能为空');
    }
    if (!config.bucketName) {
      throw new Error('bucketName不能为空');
    }
  }

  /**
   * 创建模拟S3客户端（用于测试环境）
   */
  private createMockS3Client(config: ICloudStorageConfig): S3Client {
    const mockData = new Map<string, any>();

    return {
      async headBucket(params: any): Promise<any> {
        return Promise.resolve({});
      },

      async getObject(params: any): Promise<any> {
        const data = mockData.get(params.Key);
        if (!data) {
          throw new Error('NoSuchKey');
        }
        return Promise.resolve({
          Body: data,
          ContentType: 'application/json',
          LastModified: new Date(),
          ETag: `mock-etag-${params.Key}`
        });
      },

      async putObject(params: any): Promise<any> {
        mockData.set(params.Key, params.Body);
        return Promise.resolve({
          ETag: `mock-etag-${Date.now()}`
        });
      },

      async deleteObject(params: any): Promise<any> {
        mockData.delete(params.Key);
        return Promise.resolve({});
      },

      async deleteObjects(params: any): Promise<any> {
        params.Delete.Objects.forEach((obj: any) => mockData.delete(obj.Key));
        return Promise.resolve({
          Deleted: params.Delete.Objects
        });
      },

      async listObjectsV2(params: any): Promise<any> {
        const keys = Array.from(mockData.keys());
        const filteredKeys = params.Prefix ? keys.filter(key => key.startsWith(params.Prefix)) : keys;

        return Promise.resolve({
          Contents: filteredKeys.map(key => ({
            Key: key,
            Size: JSON.stringify(mockData.get(key)).length,
            LastModified: new Date()
          }))
        });
      },

      async headObject(params: any): Promise<any> {
        const data = mockData.get(params.Key);
        if (!data) {
          throw new Error('NoSuchKey');
        }
        return Promise.resolve({
          ContentLength: JSON.stringify(data).length,
          LastModified: new Date(),
          ETag: `mock-etag-${params.Key}`,
          ContentType: 'application/json',
          Metadata: {}
        });
      },

      async createMultipartUpload(params: any): Promise<any> {
        const uploadId = `mock-upload-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        return Promise.resolve({
          UploadId: uploadId
        });
      },

      async uploadPart(params: any): Promise<any> {
        return Promise.resolve({
          ETag: `mock-part-etag-${params.PartNumber}-${Date.now()}`
        });
      },

      async completeMultipartUpload(params: any): Promise<any> {
        // 模拟完成分块上传，将数据存储到mockData
        const combinedData = `multipart-data-${params.Key}-${Date.now()}`;
        mockData.set(params.Key, combinedData);

        return Promise.resolve({
          ETag: `mock-complete-etag-${Date.now()}`
        });
      },

      async abortMultipartUpload(params: any): Promise<any> {
        return Promise.resolve({});
      },

      async listMultipartUploads(params: any): Promise<any> {
        return Promise.resolve({
          Uploads: []
        });
      }
    };
  }
}
